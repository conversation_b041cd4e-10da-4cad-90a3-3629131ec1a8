/*
  Warnings:

  - You are about to drop the `UserCareerPath` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[passwordResetToken]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "UserCareerPath" DROP CONSTRAINT "UserCareerPath_careerPathId_fkey";

-- DropForeignKey
ALTER TABLE "UserCareerPath" DROP CONSTRAINT "UserCareerPath_userId_fkey";

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "passwordResetExpires" TIMESTAMP(3),
ADD COLUMN     "passwordResetToken" TEXT;

-- DropTable
DROP TABLE "UserCareerPath";

-- CreateIndex
CREATE UNIQUE INDEX "User_passwordResetToken_key" ON "User"("passwordResetToken");
