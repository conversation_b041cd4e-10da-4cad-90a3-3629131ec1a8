/*
  Warnings:

  - You are about to drop the column `description` on the `CareerPath` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[slug]` on the table `CareerPath` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `actionableSteps` to the `CareerPath` table without a default value. This is not possible if the table is not empty.
  - Added the required column `overview` to the `CareerPath` table without a default value. This is not possible if the table is not empty.
  - Added the required column `slug` to the `CareerPath` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `CareerPath` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "CareerPath" DROP COLUMN "description",
ADD COLUMN     "actionableSteps" JSONB NOT NULL,
ADD COLUMN     "cons" TEXT[],
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "overview" TEXT NOT NULL,
ADD COLUMN     "pros" TEXT[],
ADD COLUMN     "slug" TEXT NOT NULL,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- CreateTable
CREATE TABLE "Skill" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Skill_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Industry" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Industry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SuggestionRule" (
    "id" TEXT NOT NULL,
    "careerPathId" TEXT NOT NULL,
    "questionKey" TEXT NOT NULL,
    "answerValue" JSONB NOT NULL,
    "weight" DOUBLE PRECISION NOT NULL DEFAULT 1.0,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SuggestionRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_CareerPathToSkill" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_CareerPathToSkill_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_CareerPathToIndustry" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_CareerPathToIndustry_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "Skill_name_key" ON "Skill"("name");

-- CreateIndex
CREATE UNIQUE INDEX "Industry_name_key" ON "Industry"("name");

-- CreateIndex
CREATE INDEX "SuggestionRule_careerPathId_idx" ON "SuggestionRule"("careerPathId");

-- CreateIndex
CREATE INDEX "_CareerPathToSkill_B_index" ON "_CareerPathToSkill"("B");

-- CreateIndex
CREATE INDEX "_CareerPathToIndustry_B_index" ON "_CareerPathToIndustry"("B");

-- CreateIndex
CREATE UNIQUE INDEX "CareerPath_slug_key" ON "CareerPath"("slug");

-- AddForeignKey
ALTER TABLE "SuggestionRule" ADD CONSTRAINT "SuggestionRule_careerPathId_fkey" FOREIGN KEY ("careerPathId") REFERENCES "CareerPath"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CareerPathToSkill" ADD CONSTRAINT "_CareerPathToSkill_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CareerPathToSkill" ADD CONSTRAINT "_CareerPathToSkill_B_fkey" FOREIGN KEY ("B") REFERENCES "Skill"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CareerPathToIndustry" ADD CONSTRAINT "_CareerPathToIndustry_A_fkey" FOREIGN KEY ("A") REFERENCES "CareerPath"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CareerPathToIndustry" ADD CONSTRAINT "_CareerPathToIndustry_B_fkey" FOREIGN KEY ("B") REFERENCES "Industry"("id") ON DELETE CASCADE ON UPDATE CASCADE;
