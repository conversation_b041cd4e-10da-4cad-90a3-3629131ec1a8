import prisma from '../src/lib/prisma';
// import { PrismaClient } from '@prisma/client'; // No longer needed

// const prisma = new PrismaClient(); // No longer needed

async function main() {
  console.log(`Start seeding ...`);

  // 1. Create Career Paths
  const freelanceDevPath = await prisma.careerPath.upsert({
    where: { slug: 'freelance-web-developer' },
    update: {},
    create: {
      name: 'Freelance Web Developer',
      slug: 'freelance-web-developer',
      overview: 'Build websites and web applications for clients on a project basis. Enjoy flexibility and a variety of work.',
      pros: [
        'High flexibility in work hours and location',
        'Direct control over projects and clients',
        'Potential for high income based on skill and marketing',
        'Continuous learning and skill development',
      ],
      cons: [
        'Income can be unstable, especially initially',
        'Need to handle all aspects of business (marketing, contracts, invoicing)',
        'Can be isolating if not proactive in networking',
        'Requires self-discipline and time management',
      ],
      actionableSteps: [
        'Define your niche and services offered.',
        'Build a strong portfolio showcasing your best work.',
        'Set up a professional website or online presence.',
        'Network actively online and offline to find clients.',
        'Learn about contract negotiation and project pricing.',
        'Establish a system for managing finances and taxes.',
        'Continuously update your skills and stay current with web technologies.',
      ],
      isActive: true,
    },
  });

  const onlineBusinessPath = await prisma.careerPath.upsert({
    where: { slug: 'simple-online-business' },
    update: {},
    create: {
      name: 'Simple Online Business Owner',
      slug: 'simple-online-business',
      overview: 'Create and manage a small online business, such as an e-commerce store, blog, or niche service.',
      pros: [
        'Low startup costs compared to brick-and-mortar',
        'Global reach and customer base',
        'Scalability potential',
        'Work from anywhere with an internet connection',
      ],
      cons: [
        'Requires diverse skills (marketing, sales, customer service)',
        'Can be very competitive',
        'Time-consuming to build traction and revenue',
        'Reliant on online platforms and tools',
      ],
      actionableSteps: [
        'Identify a niche market or product idea.',
        'Conduct market research to validate your idea.',
        'Choose an e-commerce platform or website builder.',
        'Develop a basic business plan and financial projections.',
        'Create compelling content and product listings.',
        'Implement a digital marketing strategy (SEO, social media).',
        'Focus on providing excellent customer service.',
      ],
      isActive: true,
    },
  });

  console.log(`Created career path: ${freelanceDevPath.name} (ID: ${freelanceDevPath.id})`);
  console.log(`Created career path: ${onlineBusinessPath.name} (ID: ${onlineBusinessPath.id})`);

  // 2. Create Suggestion Rules

  // Rule 1: Strong desire for work-life balance suggests Freelance Web Developer
  await prisma.suggestionRule.create({
    data: {
      careerPathId: freelanceDevPath.id,
      questionKey: 'desired_outcomes_work_life',
      answerValue: 'critical',
      weight: 3.0,
      notes: 'Critical need for work-life balance strongly suggests freelancing.',
    },
  });
  console.log(`Created rule for ${freelanceDevPath.name} based on work-life balance.`);

  // Rule 2: Financial anxiety is a con for Freelance, so a positive rule for financial comfort
  await prisma.suggestionRule.create({
    data: {
      careerPathId: freelanceDevPath.id,
      questionKey: 'financial_comfort',
      answerValue: 5, // Prisma handles numbers directly for JSON fields if schema allows
      weight: 1.5,
      notes: 'High financial comfort makes freelancing transition easier.',
    },
  });
  console.log(`Created rule for ${freelanceDevPath.name} based on financial comfort.`);

  // Rule 3: If lack of growth is a trigger, Online Business might be an option
  await prisma.suggestionRule.create({
    data: {
      careerPathId: onlineBusinessPath.id,
      questionKey: 'dissatisfaction_triggers',
      // For multi-select, the service logic checks if this value is IN the array of responses.
      // So, the rule's answerValue should be one of the *possible single string values* from the multi-select options.
      answerValue: 'lack_of_growth',
      weight: 2.0,
      notes: 'Lack of growth opportunities can be addressed by starting an online business.',
    },
  });
  console.log(`Created rule for ${onlineBusinessPath.name} based on dissatisfaction (lack_of_growth).`);

  console.log(`Seeding finished.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 