import { Resend } from 'resend';
import { render } from '@react-email/render';
import React from 'react';
import { PasswordResetEmail } from '../emails/PasswordResetEmail';

const resend = new Resend(process.env.RESEND_API_KEY);

interface EmailOptions {
  to: string;
  subject: string;
  template: React.ReactElement; // Use React.ReactElement for email templates
}

export async function sendEmail({ to, subject, template }: EmailOptions) {
  try {
    const html = await render(template);

    const { data, error } = await resend.emails.send({
      from: '<EMAIL>', // Use a verified sender from your Resend domain
      to,
      subject,
      html,
    });

    if (error) {
      console.error('Error sending email:', error);
      return { success: false, error: error.message };
    }

    console.log('Email sent successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('Caught an exception while sending email:', error);
    return { success: false, error: (error as Error).message };
  }
}

interface SendPasswordResetEmailParams {
  to: string;
  url: string;
}

export async function sendPasswordResetEmail(params: SendPasswordResetEmailParams) {
  const { to, url } = params;
  try {
    await resend.emails.send({
      from: '<EMAIL>', // Use a verified sender from your Resend domain
      to,
      subject: 'Reset your password',
      react: React.createElement(PasswordResetEmail, { resetLink: url }),
    });
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
} 