'use client';

import React from 'react';

const TermsOfServicePage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Terms of Service</h1>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">1. Agreement to Terms</h2>
        <p className="mb-2">
          These Terms of Service constitute a legally binding agreement made between you, whether personally or on behalf of an entity (&quot;you&quot;) and [Your Company Name] (&quot;we,&quot; &quot;us,&quot; or &quot;our&quot;), concerning your access to and use of the [Your Website URL] website as well as any other media form, media channel, mobile website or mobile application related, linked, or otherwise connected thereto (collectively, the &quot;Site&quot;).
        </p>
        <p>
          You agree that by accessing the Site, you have read, understood, and agreed to be bound by all of these Terms of Service. If you do not agree with all of these Terms of Service, then you are expressly prohibited from using the Site and you must discontinue use immediately.
        </p>
        <p className="mt-2">
          <em>Placeholder: More detailed information about the agreement will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">2. Intellectual Property Rights</h2>
        <p>
          <em>Placeholder: Detailed information about intellectual property rights will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">3. User Representations</h2>
        <p>
          <em>Placeholder: Detailed information about user representations will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">4. Prohibited Activities</h2>
        <p>
          <em>Placeholder: Detailed information about prohibited activities will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">5. Term and Termination</h2>
        <p>
          <em>Placeholder: Detailed information about term and termination will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">6. Governing Law</h2>
        <p>
          <em>Placeholder: Detailed information about governing law will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">7. Disclaimer</h2>
        <p>
          <em>Placeholder: Detailed disclaimer information will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">8. Limitation of Liability</h2>
        <p>
          <em>Placeholder: Detailed information about limitation of liability will be added here once the final legal text is available.</em>
        </p>
      </section>

      <section className="mb-6">
        <h2 className="text-2xl font-semibold mb-3">9. Updates To These Terms</h2>
        <p>
          We may update these Terms of Service from time to time. The updated version will be indicated by an updated &quot;Revised&quot; date and the updated version will be effective as soon as it is accessible. 
        </p>
        <p className="mt-2">
          <em>Last Updated: [Date will be inserted here]</em>
        </p>
      </section>

      <section>
        <h2 className="text-2xl font-semibold mb-3">10. Contact Us</h2>
        <p>
          In order to resolve a complaint regarding the Site or to receive further information regarding use of the Site, please contact us at:
        </p>
        <p className="mt-2">
          [Your Company Name]<br />
          [Your Company Address Line 1]<br />
          [Your Company Address Line 2]<br />
          [City, State, Zip Code]<br />
          [Country]<br />
          [Your Contact Email]
        </p>
      </section>
    </div>
  );
};

export default TermsOfServicePage; 