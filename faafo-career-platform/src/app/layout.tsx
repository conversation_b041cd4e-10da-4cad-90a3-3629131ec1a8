import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
// import Navigation from "../../components/Navigation"; // Navigation component is no longer used here
import SessionWrapper from '../../components/SessionWrapper';
import CookieConsentBanner from "../../components/CookieConsentBanner";
import { ThemeProvider } from "../components/layout/ThemeProvider";
import VercelAnalyticsWrapper from "./components/layout/VercelAnalyticsWrapper";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App", // Consider updating with your actual app title
  description: "Generated by create next app", // Consider updating
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          <SessionWrapper>
            {/* Skip to main content link */}
            <a href="#main-content" className="sr-only focus:not-sr-only">Skip to main content</a>
            
            {/* The header and Navigation component previously here have been removed */}
            {/* The NavigationBar component from page.tsx will now be the sole navigation */}

            <main id="main-content" className="flex-grow">
              {/* Removed container and p-4 from main to allow pages to control their own layout fully */}
              {children}
            </main>

            <footer className="w-full p-4 bg-card mt-auto">
              {/* Consider making footer background consistent if dark mode is default */}
              <div className="container mx-auto text-center text-sm text-gray-600 dark:text-gray-300">
                <p>&copy; {new Date().getFullYear()} FAAFO. All rights reserved.</p> {/* Updated company name */}
                <nav className="mt-2">
                  <a href="/privacy-policy" className="hover:underline dark:hover:text-gray-100 px-2">Privacy Policy</a>
                  <span className="px-1">|</span>
                  <a href="/terms-of-service" className="hover:underline dark:hover:text-gray-100 px-2">Terms of Service</a>
                </nav>
              </div>
            </footer>
            <CookieConsentBanner />
          </SessionWrapper>
          <VercelAnalyticsWrapper />
        </ThemeProvider>
      </body>
    </html>
  );
}
