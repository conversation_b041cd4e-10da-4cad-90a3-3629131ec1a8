import { NextResponse } from 'next/server';

const mockCareerPaths = [
  {
    id: 'strategic-switcher',
    title: 'The Strategic Switcher',
    description: 'Perfect for professionals who want security while transitioning to a new career or role',
    pros: [
      'Keep current income',
      'Lower financial risk',
      'Test new field gradually',
      'Build network while employed',
    ],
    cons: [
      'Requires time management',
      'May take longer',
      'Potential burnout',
      'Work-life balance challenges',
    ],
    actionChecklist: [
      { id: 1, text: 'Research target roles and companies', completed: false },
      { id: 2, text: 'Identify skill gaps and learning resources', completed: false },
      { id: 3, text: 'Start online course or certification', completed: false },
      { id: 4, text: 'Begin networking in target industry', completed: false },
      { id: 5, text: 'Create portfolio or side projects', completed: false },
      { id: 6, text: 'Start applying to positions', completed: false },
    ],
  },
  {
    id: 'skilled-freelancer',
    title: 'The Skilled Freelancer',
    description: 'Leverage existing skills as independent consultant',
    pros: [
      'Flexibility and autonomy',
      'High earning potential',
      'Diverse project experiences',
      'Quick start-up time',
    ],
    cons: [
      'Inconsistent income',
      'Self-discipline required',
      'No employer benefits',
      'Client acquisition challenges',
    ],
    actionChecklist: [
      { id: 1, text: 'Define your niche and services', completed: false },
      { id: 2, text: 'Set your rates and create a pricing model', completed: false },
      { id: 3, text: 'Build a strong portfolio', completed: false },
      { id: 4, text: 'Create a client acquisition strategy', completed: false },
      { id: 5, text: 'Set up legal and financial structures', completed: false },
    ],
  },
  {
    id: 'career-pivoter',
    title: 'The Career Pivoter',
    description: 'Complete career change with new skill development',
    pros: [
      'Deep satisfaction from meaningful work',
      'Long-term growth potential',
      'Opportunity to align with values',
      'Expand skill set significantly',
    ],
    cons: [
      'Significant time and financial investment',
      'Uncertainty and risk',
      'May require entry-level positions initially',
      'Steep learning curve',
    ],
    actionChecklist: [
      { id: 1, text: 'Identify your true passions and interests', completed: false },
      { id: 2, text: 'Research industries and roles that align', completed: false },
      { id: 3, text: 'Acquire new skills through courses/bootcamps', completed: false },
      { id: 4, text: 'Gain practical experience through internships/projects', completed: false },
      { id: 5, text: 'Network with professionals in the new field', completed: false },
    ],
  },
];

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (id) {
      const careerPath = mockCareerPaths.find((path) => path.id === id);
      if (careerPath) {
        return NextResponse.json(careerPath);
      }
      return new NextResponse('Career Path not found', { status: 404 });
    }

    return NextResponse.json(mockCareerPaths);
  } catch (error) {
    console.error('Error fetching career paths:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { action, pathId, itemId, completed, isBookmarked } = body;

    // Basic validation
    if (!action) {
      return new NextResponse('Action is required', { status: 400 });
    }

    switch (action) {
      case 'bookmark':
        if (!pathId || isBookmarked === undefined) {
          return new NextResponse('pathId and isBookmarked are required for bookmark action', { status: 400 });
        }
        console.log(`Bookmark action received for path: ${pathId}, status: ${isBookmarked}`);
        return NextResponse.json({ message: `Career path ${pathId} bookmarked: ${isBookmarked}.` });

      case 'update-checklist-item':
        if (!pathId || itemId === undefined || completed === undefined) {
          return new NextResponse('pathId, itemId, and completed are required for update-checklist-item action', { status: 400 });
        }
        // In a real application, you'd update the checklist item in a database
        console.log(`Update checklist item received for path: ${pathId}, item: ${itemId}, completed: ${completed}`);
        return NextResponse.json({ message: `Checklist item ${itemId} for path ${pathId} updated successfully.` });

      default:
        return new NextResponse('Invalid action', { status: 400 });
    }
  } catch (error) {
    console.error('Error processing career path POST request:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
} 