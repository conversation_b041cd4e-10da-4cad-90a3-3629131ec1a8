import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth'; 
import prisma from '@/lib/prisma';
import { Prisma } from '@/generated/prisma'; // Assuming this is your generated Prisma client type import

interface FreedomFundSaveRequest {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavingsAmount?: number;
}

// POST handler to create or update FreedomFund data
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json() as FreedomFundSaveRequest;
    const { monthlyExpenses, coverageMonths, currentSavingsAmount } = body;

    if (typeof monthlyExpenses !== 'number' || monthlyExpenses <= 0 ||
        typeof coverageMonths !== 'number' || ![3, 6, 9, 12].includes(coverageMonths) ||
        (currentSavingsAmount !== undefined && (typeof currentSavingsAmount !== 'number' || currentSavingsAmount < 0))) {
      return NextResponse.json({ error: 'Invalid input data. Please check values and try again.' }, { status: 400 });
    }

    const targetSavings = monthlyExpenses * coverageMonths;

    const freedomFundEntry = await prisma.freedomFund.upsert({
      where: { userId: session.user.id },
      update: {
        monthlyExpenses,
        coverageMonths,
        targetSavings,
        currentSavingsAmount: currentSavingsAmount === undefined ? null : currentSavingsAmount,
      },
      create: {
        userId: session.user.id,
        monthlyExpenses,
        coverageMonths,
        targetSavings,
        currentSavingsAmount: currentSavingsAmount === undefined ? null : currentSavingsAmount,
      },
    });

    return NextResponse.json(freedomFundEntry, { status: 200 });

  } catch (error) {
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON format in request body.' }, { status: 400 });
    }
    console.error('Freedom Fund POST error:', error);
    // Type guard for Prisma errors if specific handling is needed
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
        // Handle known Prisma errors, e.g. unique constraint violation
        return NextResponse.json({ error: 'Database error processing your request.' }, { status: 500 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred while saving your Freedom Fund data.' }, { status: 500 });
  }
}

// GET handler to retrieve FreedomFund data
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const freedomFundEntry = await prisma.freedomFund.findUnique({
      where: { userId: session.user.id },
    });

    if (!freedomFundEntry) {
      return NextResponse.json({ error: 'No Freedom Fund data found for this user.' }, { status: 404 });
    }

    return NextResponse.json(freedomFundEntry, { status: 200 });

  } catch (error) {
    console.error('Freedom Fund GET error:', error);
    // Type guard for Prisma errors if specific handling is needed
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
        return NextResponse.json({ error: 'Database error retrieving your Freedom Fund data.' }, { status: 500 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred while fetching your Freedom Fund data.' }, { status: 500 });
  }
} 